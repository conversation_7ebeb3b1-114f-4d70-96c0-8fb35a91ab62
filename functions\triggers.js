// NOTE: in order for exported functions to be accessible to frontend, they have to be re-exported in index.js.

// Firebase Admin SDK
const admin = require("firebase-admin");
const db = admin.firestore();
const { getAuth } = require("firebase-admin/auth");
const adminFs = require('firebase-admin/firestore'); // For saleWrite helpers
const fieldDeletionMark = adminFs.FieldValue.delete(); // For saleWrite helpers

// Firebase Functions SDK
const firestoreTriggers = require("firebase-functions/v2/firestore");
const { beforeUserSignedIn: beforeUserSignedInTrigger } = require("firebase-functions/v2/identity");

// External Libraries
const objectHash = require('object-hash'); // For logUserDocChange helpers
const jiff = require('jiff'); // For logUserDocChange helpers

// Imports from other local modules
const { regionOpts, getObjectValue, _listAllUsers, _reflectUsers } = require("./common.js");

// --- Helper functions for logUserDocChange (originally from userManagement.js) ---
function _getDataOrNullFromSnapshot(documentSnapshot) {
  const data = documentSnapshot.data();
  return data ? data : null;
}

function _daysInFuture(n) {
  return new Date(new Date().getTime() + 86400000 * n);
}

function _checkIfOpPresent(patch, opType, path, value) {
  for (const op of patch) {
    if (op["op"] === opType) {
      if (op["path"] === path) {
        if (op["value"] === value) {
          return true;
        }
      }
    }
  }
  return false;
}

function _generateLogId(timestamp, docPath, docBefore, docAfter) {
  const idHashSource = [timestamp, docPath];
  if (docBefore) idHashSource.push(docBefore);
  if (docAfter) idHashSource.push(docAfter);
  return objectHash(idHashSource);
}

function _computeDocumentHashes(docBefore, docAfter) {
  return {
    beforeHash: docBefore ? objectHash(docBefore) : null,
    afterHash: docAfter ? objectHash(docAfter) : null
  };
}

function _computeDocumentDiff(docBefore, docAfter) {
  const patch = jiff.diff(docBefore, docAfter);
  return JSON.stringify(patch);
}

function _determineDocumentStateChanges(patch, isCreation) {
  return {
    isCreation,
    isDeactivation: _checkIfOpPresent(patch, "replace", "/active", false),
    isReactivation: _checkIfOpPresent(patch, "replace", "/active", true),
    isInvalidation: _checkIfOpPresent(patch, "replace", "/invalid", true),
    isRevalidation: _checkIfOpPresent(patch, "replace", "/invalid", false)
  };
}

function _createLogDocument(stateChanges, hashes, patchString, eventData) {
  const { docBefore, docPath, docPathParams, timestamp, authId, authType } = eventData;
  const oneYearFromNow = _daysInFuture(365);
  const ttl = oneYearFromNow.toISOString();
  return {
    ...stateChanges,
    ttl,
    beforeHash: hashes.beforeHash,
    afterHash: hashes.afterHash,
    docBefore,
    patchString,
    docPath,
    docPathParams,
    timestamp,
    authId,
    authType,
  };
}

async function _saveLogDocument(logId, logDoc) {
  const logCollection = db.collection("log");
  const logRef = logCollection.doc(logId);
  await logRef.set(logDoc);
}

// --- Helper functions for saleWrite (originally from common.js) ---
function determineSaleActiveState(saleDoc) {
  const activeRaw = saleDoc['active'];
  return activeRaw == null ? true : activeRaw;
}

function checkTimestampStatus(isActive, lastDeactivated) {
  const lastDeactivatedDefined = lastDeactivated != null;
  return {
    timestampMissing: !isActive && !lastDeactivatedDefined,
    timestampShouldBeRemoved: isActive && lastDeactivatedDefined
  };
}

async function addDeactivationTimestamp(docRef) {
  const timestamp = Date.now();
  console.log("adding timestamp");
  return docRef.set(
    {"lastDeactivated": timestamp},
    { merge: true }
  );
}

async function removeDeactivationTimestamp(docRef) {
  console.log("removing timestamp");
  return docRef.set(
    {"lastDeactivated": fieldDeletionMark},
    { merge: true }
  );
}

// Common function to handle document change logging
async function _handleDocChangeLogging(authEvent, loggerName) {
  const authType = authEvent.authType;
  const authId = authEvent.authId;

  const docAfter = authEvent.data.after.exists ? _getDataOrNullFromSnapshot(authEvent.data.after) : null;
  const docBefore = authEvent.data.before.exists ? _getDataOrNullFromSnapshot(authEvent.data.before) : null;

  if (docBefore === null && docAfter === null) {
    console.log(`${loggerName}: Both before and after snapshots are null. Skipping.`);
    return null;
  }

  const docPath = authEvent.document;
  const docPathParams = authEvent.params;
  const timestamp = authEvent.time;

  const logId = _generateLogId(timestamp, docPath, docBefore, docAfter);
  const hashes = _computeDocumentHashes(docBefore, docAfter);
  const patchString = _computeDocumentDiff(docBefore, docAfter);
  const patch = JSON.parse(patchString);

  const isCreation = !authEvent.data.before.exists && authEvent.data.after.exists;

  const stateChanges = _determineDocumentStateChanges(patch, isCreation);
  const eventData = { docBefore, docPath, docPathParams, timestamp, authId, authType };
  const logDoc = _createLogDocument(stateChanges, hashes, patchString, eventData);

  await _saveLogDocument(logId, logDoc);
}

// Helper function to verify user email on sign-in
async function _verifyUserEmailOnSignIn(uid) {
  try {
    // Get the current user to check if email is already verified
    const userRecord = await getAuth().getUser(uid);

    if (!userRecord.emailVerified) {
      console.log(`Marking email as verified for user ${uid}`);
      await getAuth().updateUser(uid, { emailVerified: true });
      console.log(`Successfully verified email for user ${uid}`);
    } else {
      console.log(`Email already verified for user ${uid}`);
    }
  } catch (error) {
    console.error(`Error verifying email for user ${uid}:`, error);
    // Don't throw the error to avoid blocking the sign-in process
  }
}

// --- Trigger Functions ---

exports.reflectBeforeUserSignedIn = beforeUserSignedInTrigger(
  regionOpts,
  async (event) => {
    console.log("beforeUserSignedIn triggered");
    const signingInUserUid = event.data.uid;

    // First, verify the user's email if not already verified
    await _verifyUserEmailOnSignIn(signingInUserUid);

    // Then reflect users to Firestore
    return _reflectUsers(signingInUserUid);
  }
);

exports.updateAdminClaims = firestoreTriggers.onDocumentWritten(
  {
    region: regionOpts.region,
    document: "admin/list"
  },
  async (event) => {
    const docAfterData = event.data.after.data();
    if (!docAfterData) {
        console.log("updateAdminClaims triggered for a delete event on admin/list, skipping.");
        return null;
    }
    const adminUidsFieldName = 'admin-uids';
    const adminUidsAfter = getObjectValue(docAfterData, adminUidsFieldName, []);
    console.log( "updateAdminClaims:", "\nadminUidsAfter", adminUidsAfter);

    const allUsers = await _listAllUsers(null);
    const promises = Object.values(allUsers).map(async user => {
      const uid = user.uid;
      const shouldBeAdmin = adminUidsAfter.includes(uid);
      const isAdmin = user.admin;
      const shouldChangeSomething = shouldBeAdmin !== isAdmin;

      if (shouldChangeSomething) {
        console.log(`Updating admin status for user ${uid} to ${shouldBeAdmin}`);
        await getAuth().setCustomUserClaims(uid, { admin: shouldBeAdmin });
        await admin.auth().revokeRefreshTokens(uid);
        await _reflectUsers();
      }
    });
    await Promise.all(promises);
  }
);

exports.logUserRootDocChange = firestoreTriggers.onDocumentWrittenWithAuthContext(
  {
    region: regionOpts.region,
    document: "user/{pathUserId}",
  },
  async (authEvent) => {
    return _handleDocChangeLogging(authEvent, "logUserRootDocChange");
  }
);

exports.logUserDocChange = firestoreTriggers.onDocumentWrittenWithAuthContext(
  {
    region: regionOpts.region,
    document: "user/{pathUserId}/{pathUserCol}/{pathDocId}",
  },
  async (authEvent) => {
    return _handleDocChangeLogging(authEvent, "logUserDocChange");
  }
);

exports.logProductDocChange = firestoreTriggers.onDocumentWrittenWithAuthContext(
  {
    region: regionOpts.region,
    document: "product/{pathProductId}",
  },
  async (authEvent) => {
    return _handleDocChangeLogging(authEvent, "logProductDocChange");
  }
);

exports.saleWrite = firestoreTriggers.onDocumentWritten(
  {
    region: regionOpts.region,
    document: "**/sale/{saleId}",
  },
  (event) => {
    if (!event.data.after.exists) {
      console.log("saleWrite: Document was deleted. No action taken.");
      return null;
    }
    const current = event.data.after.data();

    const isActive = determineSaleActiveState(current);
    const lastDeactivated = current['lastDeactivated'];
    const { timestampMissing, timestampShouldBeRemoved } =
      checkTimestampStatus(isActive, lastDeactivated);

    if (timestampMissing) {
      return addDeactivationTimestamp(event.data.after.ref);
    } else if (timestampShouldBeRemoved) {
      return removeDeactivationTimestamp(event.data.after.ref);
    }
    return null;
  }
);